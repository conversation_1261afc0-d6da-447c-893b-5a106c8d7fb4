# ===================================
# SENSITIVE FILES - NEVER COMMIT THESE
# ===================================

# Environment variables and API keys
.env
.env.local
.env.development
.env.test
.env.production
*.env

# API Keys and secrets
api_keys.txt
secrets.txt
config.json
credentials.json
firebase-service-account*.json
service-account*.json
*-key.json
*.pem
*.p12

# Generated environment files
production.env
docker.env
set_env_vars.ps1
.env.production
.env.staging

# ===================================
# PYTHON
# ===================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ===================================
# LOGS AND DATABASES
# ===================================

# Log files
*.log
logs/
*.log.*
log/

# Database files
*.db
*.sqlite
*.sqlite3
*.db-journal

# ===================================
# OPERATING SYSTEM
# ===================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===================================
# EDITORS AND IDEs
# ===================================

# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===================================
# TEMPORARY AND CACHE FILES
# ===================================

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*.old
*.orig

# Cache directories
.cache/
cache/
tmp/
temp/

# ===================================
# DEPLOYMENT AND BUILD
# ===================================

# Deployment files
.vercel
.netlify
.serverless/

# Build outputs
build/
dist/
out/

# ===================================
# PROJECT SPECIFIC
# ===================================

# ParadoxGPT specific
test_responses/
debug_output/
conversation_history/
user_data/

# Any personal notes or documentation
notes.txt
todo.txt
personal/
private/

# Backup files
*.backup
backup/
backups/
